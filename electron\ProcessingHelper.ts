// ProcessingHelper.ts
import fs from "node:fs"
import path from "node:path"
import { ScreenshotHelper } from "./ScreenshotHelper"
import { IProcessingHelperDeps } from "./main"
import * as axios from "axios"
import { app, BrowserWindow, dialog } from "electron"
import { OpenAI } from "openai"
import { configHelper } from "./ConfigHelper"
import Anthropic from '@anthropic-ai/sdk';

// Interface for Gemini API requests
interface GeminiMessage {
  role: string;
  parts: Array<{
    text?: string;
    inlineData?: {
      mimeType: string;
      data: string;
    }
  }>;
}

interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
    };
    finishReason: string;
  }>;
}
interface AnthropicMessage {
  role: 'user' | 'assistant';
  content: Array<{
    type: 'text' | 'image';
    text?: string;
    source?: {
      type: 'base64';
      media_type: string;
      data: string;
    };
  }>;
}
export class ProcessingHelper {
  private deps: IProcessingHelperDeps
  private screenshotHelper: ScreenshotHelper
  private openaiClient: OpenAI | null = null
  private geminiApiKey: string | null = null
  private anthropicClient: Anthropic | null = null
  private openrouterApiKey: string | null = null

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps
    this.screenshotHelper = deps.getScreenshotHelper()

    // Initialize AI client based on config
    this.initializeAIClient();

    // Listen for config changes to re-initialize the AI client
    configHelper.on('config-updated', () => {
      this.initializeAIClient();
    });
  }

  /**
   * Initialize or reinitialize the AI client with current config
   */
  private initializeAIClient(): void {
    try {
      const config = configHelper.loadConfig();

      // Reset all clients first
      this.openaiClient = null;
      this.geminiApiKey = null;
      this.anthropicClient = null;
      this.openrouterApiKey = null;

      if (config.apiProvider === "openai") {
        if (config.apiKey) {
          this.openaiClient = new OpenAI({
            apiKey: config.apiKey,
            timeout: 60000, // 60 second timeout
            maxRetries: 2   // Retry up to 2 times
          });
          console.log("OpenAI client initialized successfully");
        } else {
          console.warn("No API key available, OpenAI client not initialized");
        }
      } else if (config.apiProvider === "gemini"){
        // Gemini client initialization
        if (config.apiKey) {
          this.geminiApiKey = config.apiKey;
          console.log("Gemini API key set successfully");
        } else {
          console.warn("No API key available, Gemini client not initialized");
        }
      } else if (config.apiProvider === "anthropic") {
        if (config.apiKey) {
          this.anthropicClient = new Anthropic({
            apiKey: config.apiKey,
            timeout: 60000,
            maxRetries: 2
          });
          console.log("Anthropic client initialized successfully");
        } else {
          console.warn("No API key available, Anthropic client not initialized");
        }
      } else if (config.apiProvider === "openrouter") {
        // For OpenRouter, we'll use Gemini for image processing and OpenRouter for text
        if (config.apiKey) {
          this.openrouterApiKey = config.apiKey;

          // For image support, we need to initialize Gemini as well
          if (config.extractionModel === "gemini-2.0-flash" || config.debuggingModel === "gemini-2.0-flash") {
            // Use the stored Gemini API key
            if (config.geminiApiKey) {
              this.geminiApiKey = config.geminiApiKey;
              console.log("Using stored Gemini API key for image processing");
            } else {
              console.warn("No Gemini API key available for image processing");
            }
          }

          console.log("OpenRouter API key set successfully");
        } else {
          console.warn("No API key available, OpenRouter client not initialized");
        }
      }
    } catch (error) {
      console.error("Failed to initialize AI client:", error);
      this.openaiClient = null;
      this.geminiApiKey = null;
      this.anthropicClient = null;
      this.openrouterApiKey = null;
    }
  }

  private async waitForInitialization(
    mainWindow: BrowserWindow
  ): Promise<void> {
    let attempts = 0
    const maxAttempts = 50 // 5 seconds total

    while (attempts < maxAttempts) {
      const isInitialized = await mainWindow.webContents.executeJavaScript(
        "window.__IS_INITIALIZED__"
      )
      if (isInitialized) return
      await new Promise((resolve) => setTimeout(resolve, 100))
      attempts++
    }
    throw new Error("App failed to initialize after 5 seconds")
  }

  private async getCredits(): Promise<number> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return 999 // Unlimited credits in this version

    try {
      await this.waitForInitialization(mainWindow)
      return 999 // Always return sufficient credits to work
    } catch (error) {
      console.error("Error getting credits:", error)
      return 999 // Unlimited credits as fallback
    }
  }

  private async getLanguage(): Promise<string> {
    try {
      // Get language from config
      const config = configHelper.loadConfig();
      if (config.language) {
        return config.language;
      }

      // Fallback to window variable if config doesn't have language
      const mainWindow = this.deps.getMainWindow()
      if (mainWindow) {
        try {
          await this.waitForInitialization(mainWindow)
          const language = await mainWindow.webContents.executeJavaScript(
            "window.__LANGUAGE__"
          )

          if (
            typeof language === "string" &&
            language !== undefined &&
            language !== null
          ) {
            return language;
          }
        } catch (err) {
          console.warn("Could not get language from window", err);
        }
      }

      // Default fallback
      return "python";
    } catch (error) {
      console.error("Error getting language:", error)
      return "python"
    }
  }

  public async processScreenshots(forceMCQMode: boolean = false): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    const config = configHelper.loadConfig();

    // First verify we have a valid AI client
    if (config.apiProvider === "openai" && !this.openaiClient) {
      this.initializeAIClient();

      if (!this.openaiClient) {
        console.error("OpenAI client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "gemini" && !this.geminiApiKey) {
      this.initializeAIClient();

      if (!this.geminiApiKey) {
        console.error("Gemini API key not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "anthropic" && !this.anthropicClient) {
      // Add check for Anthropic client
      this.initializeAIClient();

      if (!this.anthropicClient) {
        console.error("Anthropic client not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }
    } else if (config.apiProvider === "openrouter") {
      // Add check for OpenRouter client
      this.initializeAIClient();

      if (!this.openrouterApiKey) {
        console.error("OpenRouter API key not initialized");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID
        );
        return;
      }

      // For image processing with OpenRouter, we need Gemini
      if ((config.extractionModel === "gemini-2.0-flash" || config.debuggingModel === "gemini-2.0-flash") && !this.geminiApiKey) {
        console.error("Gemini API key not available for image processing with OpenRouter");
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.API_KEY_INVALID,
          { message: "Gemini API key is required for image processing. Please add it in settings." }
        );
        return;
      }
    }

    const view = this.deps.getView()
    console.log("Processing screenshots in view:", view)

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue()
      console.log("Processing main queue screenshots:", screenshotQueue)

      // Check if the queue is empty
      if (!screenshotQueue || screenshotQueue.length === 0) {
        console.log("No screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      // Check that files actually exist
      const existingScreenshots = screenshotQueue.filter(path => fs.existsSync(path));
      if (existingScreenshots.length === 0) {
        console.log("Screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      try {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        const screenshots = await Promise.all(
          existingScreenshots.map(async (path) => {
            try {
              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);

        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data");
        }

        const result = await this.processScreenshotsHelper(validScreenshots, signal, forceMCQMode)

        if (!result.success) {
          console.log("Processing failed:", result.error)
          if (result.error?.includes("API Key") || result.error?.includes("OpenAI") || result.error?.includes("Gemini")) {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.API_KEY_INVALID
            )
          } else {
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
              result.error
            )
          }
          // Reset view back to queue on error
          console.log("Resetting view to queue due to error")
          this.deps.setView("queue")
          return
        }

        // Only set view to solutions if processing succeeded
        console.log("Setting view to solutions after successful processing")

        // Send solution success event
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )

        // Add a small delay before changing view to ensure UI updates properly
        // This helps especially with the Qwen model
        console.log("Adding delay before changing view to ensure UI updates properly")
        setTimeout(() => {
          this.deps.setView("solutions")
        }, 200)
      } catch (error: any) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          error
        )
        console.error("Processing error:", error)
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            "Processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            error.message || "Server error. Please try again."
          )
        }
        // Reset view back to queue on error
        console.log("Resetting view to queue due to error")
        this.deps.setView("queue")
      } finally {
        this.currentProcessingAbortController = null
      }
    } else {
      // view == 'solutions'
      const extraScreenshotQueue =
        this.screenshotHelper.getExtraScreenshotQueue()
      console.log("Processing extra queue screenshots:", extraScreenshotQueue)

      // Check if the extra queue is empty
      if (!extraScreenshotQueue || extraScreenshotQueue.length === 0) {
        console.log("No extra screenshots found in queue");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);

        return;
      }

      // Check that files actually exist
      const existingExtraScreenshots = extraScreenshotQueue.filter(path => fs.existsSync(path));
      if (existingExtraScreenshots.length === 0) {
        console.log("Extra screenshot files don't exist on disk");
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS);
        return;
      }

      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START)

      // Initialize AbortController
      this.currentExtraProcessingAbortController = new AbortController()
      const { signal } = this.currentExtraProcessingAbortController

      try {
        // Get all screenshots (both main and extra) for processing
        const allPaths = [
          ...this.screenshotHelper.getScreenshotQueue(),
          ...existingExtraScreenshots
        ];

        const screenshots = await Promise.all(
          allPaths.map(async (path) => {
            try {
              if (!fs.existsSync(path)) {
                console.warn(`Screenshot file does not exist: ${path}`);
                return null;
              }

              return {
                path,
                preview: await this.screenshotHelper.getImagePreview(path),
                data: fs.readFileSync(path).toString('base64')
              };
            } catch (err) {
              console.error(`Error reading screenshot ${path}:`, err);
              return null;
            }
          })
        )

        // Filter out any nulls from failed screenshots
        const validScreenshots = screenshots.filter(Boolean);

        if (validScreenshots.length === 0) {
          throw new Error("Failed to load screenshot data for debugging");
        }

        console.log(
          "Combined screenshots for processing:",
          validScreenshots.map((s) => s.path)
        )

        const result = await this.processExtraScreenshotsHelper(
          validScreenshots,
          signal
        )

        if (result.success) {
          this.deps.setHasDebugged(true)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            result.error
          )
        }
      } catch (error: any) {
        if (axios.isCancel(error)) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            "Extra processing was canceled by the user."
          )
        } else {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            error.message
          )
        }
      } finally {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  private async processScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal,
    forceMCQMode: boolean = false
  ) {
    try {
      const config = configHelper.loadConfig();
      const language = await this.getLanguage();
      const mainWindow = this.deps.getMainWindow();

      // Step 1: Extract problem info using AI Vision API (OpenAI or Gemini)
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      // Update the user on progress
      if (mainWindow) {
        const message = forceMCQMode
          ? "Analyzing MCQ/Guess-Output question from screenshots..."
          : "Analyzing problem from screenshots...";
        mainWindow.webContents.send("processing-status", {
          message,
          progress: 20
        });
      }

      let problemInfo;

      if (config.apiProvider === "openai") {
        // Verify OpenAI client
        if (!this.openaiClient) {
          this.initializeAIClient(); // Try to reinitialize

          if (!this.openaiClient) {
            return {
              success: false,
              error: "OpenAI API key not configured or invalid. Please check your settings."
            };
          }
        }

        // Create system prompt based on force mode
        let systemPrompt;
        let userPrompt;

        if (forceMCQMode) {
          systemPrompt = `You are a MCQ and code output question interpreter. This is a FORCED MCQ/GUESS-OUTPUT mode - treat this screenshot as containing either a multiple-choice question or a guess-the-output question.

FORCED MODE INSTRUCTIONS:
- Always set question_type to either 'mcq' or 'guess-output'
- If you see code with "What will be the output?" or similar, use 'guess-output'
- Otherwise, use 'mcq'
- Extract ALL visible text as the problem_statement
- For guess-output questions: Include the COMPLETE code and any explanatory text
- If you see multiple choice options (A, B, C, D), include them in mcq_data
- If no clear options are visible, create basic structure with empty options array

Return JSON format:
{
  "problem_statement": "COMPLETE question text including ALL code blocks and explanations",
  "question_type": "mcq" or "guess-output",
  "mcq_data": {
    "question": "COMPLETE question including code - same as problem_statement",
    "options": [{"id": "A", "text": "option text"}, ...]
  }
}

CRITICAL FOR GUESS-OUTPUT QUESTIONS:
- Include ALL code snippets using markdown code blocks
- Include any explanatory text about compiler behavior, output variations, etc.
- The mcq_data.question should contain the COMPLETE question including all code
- Do NOT include correct_answer or explanation fields - only extract the question and options`;
          userPrompt = `FORCED MCQ/GUESS-OUTPUT MODE: Analyze this screenshot as a multiple-choice or guess-the-output question. Extract all text and code blocks.`;
        } else {
          systemPrompt = "You are a coding challenge, MCQ, and code output interpreter. Analyze the screenshot and determine if it contains a coding problem, multiple-choice question (MCQ), or guess-the-output question. Return the information in JSON format.\n\nFor coding problems, include these fields: problem_statement, constraints, example_input, example_output, question_type: 'coding', default_function_signature (if any function signature, class definition, or method stub is provided in the problem).\n\nFor MCQs, include these fields: problem_statement (the question text), question_type: 'mcq', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nFor guess-the-output questions, include these fields: problem_statement (the question text), question_type: 'guess-output', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nQUESTION TYPE DETECTION:\n- 'coding': Traditional algorithmic problems requiring code implementation\n- 'mcq': Multiple choice questions about concepts, theory, or code analysis\n- 'guess-output': Questions asking \"What will be the output of this code?\" or similar, where you need to predict program execution results\n\nCRITICAL FOR MCQs AND GUESS-OUTPUT WITH CODE: If the question contains code snippets, code blocks, function definitions, or programming examples (like [P1], [P2], [P3] sections), you MUST include ALL of them in the question text with proper formatting. Use markdown code blocks (```language\\ncode\\n```) to preserve the code structure, indentation, and syntax. Include the complete context - if there are multiple code examples or functions, include ALL of them. This is essential for questions about code output, memory allocation, variable scope, or program behavior.\n\nEXAMPLE: If you see functions labeled [P1], [P2], [P3] or similar, include each one as a separate code block in the question text.\n\nIMPORTANT FOR MCQs AND GUESS-OUTPUT: Do NOT include correct_answer or explanation fields in mcq_data. Only extract the question text and available options. The answer determination will be handled by a separate processing step.\n\nIMPORTANT: Look carefully for any default function signatures, class definitions, method stubs, or starter code templates provided in the problem (common on platforms like LeetCode, GeeksforGeeks, HackerRank, etc.). If found, include them exactly as shown in the 'default_function_signature' field.\n\nJust return the structured JSON without any other text.";
          userPrompt = `Extract the coding problem details from these screenshots. Return in JSON format. Preferred coding language we gonna use for this problem is ${language}.`;
        }

        // Use OpenAI for processing
        const messages = [
          {
            role: "system" as const,
            content: systemPrompt
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const,
                text: userPrompt
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        // Send to OpenAI Vision API
        const extractionResponse = await this.openaiClient.chat.completions.create({
          model: config.extractionModel || "gpt-4o",
          messages: messages,
          max_tokens: 4000,
          temperature: 0.2
        });

        // Parse the response
        try {
          const responseText = extractionResponse.choices[0].message.content;
          // Handle when OpenAI might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error) {
          console.error("Error parsing OpenAI response:", error);
          return {
            success: false,
            error: "Failed to parse problem information. Please try again or use clearer screenshots."
          };
        }
      } else if (config.apiProvider === "gemini")  {
        // Use Gemini API
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try {
          // Create prompt based on force mode
          let geminiPrompt;
          if (forceMCQMode) {
            geminiPrompt = `You are a MCQ and code output question interpreter. This is a FORCED MCQ/GUESS-OUTPUT mode - treat this screenshot as containing either a multiple-choice question or a guess-the-output question.

FORCED MODE INSTRUCTIONS:
- Always set question_type to either 'mcq' or 'guess-output'
- If you see code with "What will be the output?" or similar, use 'guess-output'
- Otherwise, use 'mcq'
- Extract ALL visible text as the problem_statement
- For guess-output questions: Include the COMPLETE code and any explanatory text
- If you see multiple choice options (A, B, C, D), include them in mcq_data
- If no clear options are visible, create basic structure with empty options array

Return JSON format:
{
  "problem_statement": "COMPLETE question text including ALL code blocks and explanations",
  "question_type": "mcq" or "guess-output",
  "mcq_data": {
    "question": "COMPLETE question including code - same as problem_statement",
    "options": [{"id": "A", "text": "option text"}, ...]
  }
}

CRITICAL FOR GUESS-OUTPUT QUESTIONS:
- Include ALL code snippets using markdown code blocks
- Include any explanatory text about compiler behavior, output variations, etc.
- The mcq_data.question should contain the COMPLETE question including all code
- Do NOT include correct_answer or explanation fields - only extract the question and options`;
          } else {
            geminiPrompt = `You are a coding challenge, MCQ, and code output interpreter. Analyze the screenshot and determine if it contains a coding problem, multiple-choice question (MCQ), or guess-the-output question. Return the information in JSON format.\n\nFor coding problems, include these fields: problem_statement, constraints, example_input, example_output, question_type: 'coding', default_function_signature (if any function signature, class definition, or method stub is provided in the problem).\n\nFor MCQs, include these fields: problem_statement (the question text), question_type: 'mcq', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nFor guess-the-output questions, include these fields: problem_statement (the question text), question_type: 'guess-output', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nQUESTION TYPE DETECTION:\n- 'coding': Traditional algorithmic problems requiring code implementation\n- 'mcq': Multiple choice questions about concepts, theory, or code analysis\n- 'guess-output': Questions asking \"What will be the output of this code?\" or similar, where you need to predict program execution results\n\nCRITICAL FOR MCQs AND GUESS-OUTPUT WITH CODE: If the question contains code snippets, code blocks, function definitions, or programming examples (like [P1], [P2], [P3] sections), you MUST include ALL of them in the question text with proper formatting. Use markdown code blocks (\`\`\`language\\ncode\\n\`\`\`) to preserve the code structure, indentation, and syntax. Include the complete context - if there are multiple code examples or functions, include ALL of them. This is essential for questions about code output, memory allocation, variable scope, or program behavior.\n\nEXAMPLE: If you see functions labeled [P1], [P2], [P3] or similar, include each one as a separate code block in the question text.\n\nIMPORTANT FOR MCQs AND GUESS-OUTPUT: Do NOT include correct_answer or explanation fields in mcq_data. Only extract the question text and available options. The answer determination will be handled by a separate processing step.\n\nIMPORTANT: Look carefully for any default function signatures, class definitions, method stubs, or starter code templates provided in the problem (common on platforms like LeetCode, GeeksforGeeks, HackerRank, etc.). If found, include them exactly as shown in the 'default_function_signature' field.\n\nJust return the structured JSON without any other text. Preferred coding language we gonna use for this problem is ${language}.`;
          }

          // Create Gemini message structure
          const geminiMessages: GeminiMessage[] = [
            {
              role: "user",
              parts: [
                {
                  text: geminiPrompt
                },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.extractionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          const responseText = responseData.candidates[0].content.parts[0].text;

          // Handle when Gemini might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error) {
          console.error("Error using Gemini API:", error);
          return {
            success: false,
            error: "Failed to process with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "openrouter") {
        // For OpenRouter, we use Gemini for image processing (extraction)
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured for image processing. Please add a Gemini API key in settings."
          };
        }

        // Use the same Gemini code for extraction as above
        try {
          // Create prompt based on force mode
          let geminiPrompt;
          if (forceMCQMode) {
            geminiPrompt = `You are a MCQ and code output question interpreter. This is a FORCED MCQ/GUESS-OUTPUT mode - treat this screenshot as containing either a multiple-choice question or a guess-the-output question.

FORCED MODE INSTRUCTIONS:
- Always set question_type to either 'mcq' or 'guess-output'
- If you see code with "What will be the output?" or similar, use 'guess-output'
- Otherwise, use 'mcq'
- Extract ALL visible text as the problem_statement
- For guess-output questions: Include the COMPLETE code and any explanatory text
- If you see multiple choice options (A, B, C, D), include them in mcq_data
- If no clear options are visible, create basic structure with empty options array

Return JSON format:
{
  "problem_statement": "COMPLETE question text including ALL code blocks and explanations",
  "question_type": "mcq" or "guess-output",
  "mcq_data": {
    "question": "COMPLETE question including code - same as problem_statement",
    "options": [{"id": "A", "text": "option text"}, ...]
  }
}

CRITICAL FOR GUESS-OUTPUT QUESTIONS:
- Include ALL code snippets using markdown code blocks
- Include any explanatory text about compiler behavior, output variations, etc.
- The mcq_data.question should contain the COMPLETE question including all code
- Do NOT include correct_answer or explanation fields - only extract the question and options`;
          } else {
            geminiPrompt = `You are a coding challenge, MCQ, and code output interpreter. Analyze the screenshot and determine if it contains a coding problem, multiple-choice question (MCQ), or guess-the-output question. Return the information in JSON format.\n\nFor coding problems, include these fields: problem_statement, constraints, example_input, example_output, question_type: 'coding', default_function_signature (if any function signature, class definition, or method stub is provided in the problem).\n\nFor MCQs, include these fields: problem_statement (the question text), question_type: 'mcq', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nFor guess-the-output questions, include these fields: problem_statement (the question text), question_type: 'guess-output', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nQUESTION TYPE DETECTION:\n- 'coding': Traditional algorithmic problems requiring code implementation\n- 'mcq': Multiple choice questions about concepts, theory, or code analysis\n- 'guess-output': Questions asking \"What will be the output of this code?\" or similar, where you need to predict program execution results\n\nCRITICAL FOR MCQs AND GUESS-OUTPUT WITH CODE: If the question contains code snippets, code blocks, function definitions, or programming examples (like [P1], [P2], [P3] sections), you MUST include ALL of them in the question text with proper formatting. Use markdown code blocks (\`\`\`language\\ncode\\n\`\`\`) to preserve the code structure, indentation, and syntax. Include the complete context - if there are multiple code examples or functions, include ALL of them. This is essential for questions about code output, memory allocation, variable scope, or program behavior.\n\nEXAMPLE: If you see functions labeled [P1], [P2], [P3] or similar, include each one as a separate code block in the question text.\n\nIMPORTANT FOR MCQs AND GUESS-OUTPUT: Do NOT include correct_answer or explanation fields in mcq_data. Only extract the question text and available options. The answer determination will be handled by a separate processing step.\n\nIMPORTANT: Look carefully for any default function signatures, class definitions, method stubs, or starter code templates provided in the problem (common on platforms like LeetCode, GeeksforGeeks, HackerRank, etc.). If found, include them exactly as shown in the 'default_function_signature' field.\n\nJust return the structured JSON without any other text. Preferred coding language we gonna use for this problem is ${language}.`;
          }

          // Create Gemini message structure
          const geminiMessages = [
            {
              role: "user",
              parts: [
                {
                  text: geminiPrompt
                },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          const responseText = responseData.candidates[0].content.parts[0].text;

          // Handle when Gemini might wrap the JSON in markdown code blocks
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error) {
          console.error("Error using Gemini API for extraction with OpenRouter:", error);
          return {
            success: false,
            error: "Failed to process with Gemini API. Please check your Gemini API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try {
          // Create prompt based on force mode
          let anthropicPrompt;
          if (forceMCQMode) {
            anthropicPrompt = `You are a MCQ and code output question interpreter. This is a FORCED MCQ/GUESS-OUTPUT mode - treat this screenshot as containing either a multiple-choice question or a guess-the-output question.

FORCED MODE INSTRUCTIONS:
- Always set question_type to either 'mcq' or 'guess-output'
- If you see code with "What will be the output?" or similar, use 'guess-output'
- Otherwise, use 'mcq'
- Extract ALL visible text as the problem_statement
- For guess-output questions: Include the COMPLETE code and any explanatory text
- If you see multiple choice options (A, B, C, D), include them in mcq_data
- If no clear options are visible, create basic structure with empty options array

Return JSON format:
{
  "problem_statement": "COMPLETE question text including ALL code blocks and explanations",
  "question_type": "mcq" or "guess-output",
  "mcq_data": {
    "question": "COMPLETE question including code - same as problem_statement",
    "options": [{"id": "A", "text": "option text"}, ...]
  }
}

CRITICAL FOR GUESS-OUTPUT QUESTIONS:
- Include ALL code snippets using markdown code blocks
- Include any explanatory text about compiler behavior, output variations, etc.
- The mcq_data.question should contain the COMPLETE question including all code
- Do NOT include correct_answer or explanation fields - only extract the question and options`;
          } else {
            anthropicPrompt = `Analyze the screenshot and determine if it contains a coding problem, multiple-choice question (MCQ), or guess-the-output question. Return the information in JSON format.\n\nFor coding problems, include these fields: problem_statement, constraints, example_input, example_output, question_type: 'coding', default_function_signature (if any function signature, class definition, or method stub is provided in the problem).\n\nFor MCQs, include these fields: problem_statement (the question text), question_type: 'mcq', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nFor guess-the-output questions, include these fields: problem_statement (the question text), question_type: 'guess-output', mcq_data: { question, options: [{id: 'A', text: 'option text'}, ...] }.\n\nQUESTION TYPE DETECTION:\n- 'coding': Traditional algorithmic problems requiring code implementation\n- 'mcq': Multiple choice questions about concepts, theory, or code analysis\n- 'guess-output': Questions asking \"What will be the output of this code?\" or similar, where you need to predict program execution results\n\nCRITICAL FOR MCQs AND GUESS-OUTPUT WITH CODE: If the question contains code snippets, code blocks, function definitions, or programming examples (like [P1], [P2], [P3] sections), you MUST include ALL of them in the question text with proper formatting. Use markdown code blocks (\`\`\`language\\ncode\\n\`\`\`) to preserve the code structure, indentation, and syntax. Include the complete context - if there are multiple code examples or functions, include ALL of them. This is essential for questions about code output, memory allocation, variable scope, or program behavior.\n\nEXAMPLE: If you see functions labeled [P1], [P2], [P3] or similar, include each one as a separate code block in the question text.\n\nIMPORTANT FOR MCQs AND GUESS-OUTPUT: Do NOT include correct_answer or explanation fields in mcq_data. Only extract the question text and available options. The answer determination will be handled by a separate processing step.\n\nIMPORTANT: Look carefully for any default function signatures, class definitions, method stubs, or starter code templates provided in the problem (common on platforms like LeetCode, GeeksforGeeks, HackerRank, etc.). If found, include them exactly as shown in the 'default_function_signature' field.\n\nJust return the structured JSON without any other text. Preferred coding language is ${language}.`;
          }

          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: anthropicPrompt
                },
                ...imageDataList.map(data => ({
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const,
                    data: data
                  }
                }))
              ]
            }
          ];

          const response = await this.anthropicClient.messages.create({
            model: config.extractionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          const responseText = (response.content[0] as { type: 'text', text: string }).text;
          const jsonText = responseText.replace(/```json|```/g, '').trim();
          problemInfo = JSON.parse(jsonText);
        } catch (error: any) {
          console.error("Error using Anthropic API:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to process with Anthropic API. Please check your API key or try again later."
          };
        }
      }

      // Update the user on progress
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Problem analyzed successfully. Preparing to generate solution...",
          progress: 40
        });
      }

      // Force MCQ mode if requested (Ctrl+Shift+S shortcut)
      if (forceMCQMode) {
        console.log("🚀 FORCE MCQ MODE: Overriding question type detection");
        console.log("🚀 FORCE MCQ MODE: Original question type:", problemInfo.question_type);

        // Force the question type to be MCQ if not already MCQ or guess-output
        if (problemInfo.question_type !== 'mcq' && problemInfo.question_type !== 'guess-output') {
          // Default to 'mcq' but could be 'guess-output' based on content analysis
          const hasCodeOutput = problemInfo.problem_statement &&
            (problemInfo.problem_statement.toLowerCase().includes('output') ||
             problemInfo.problem_statement.toLowerCase().includes('print') ||
             problemInfo.problem_statement.toLowerCase().includes('result') ||
             problemInfo.problem_statement.includes('#include') ||
             problemInfo.problem_statement.includes('printf') ||
             problemInfo.problem_statement.includes('cout') ||
             problemInfo.problem_statement.includes('System.out'));

          problemInfo.question_type = hasCodeOutput ? 'guess-output' : 'mcq';
          console.log("🚀 FORCE MCQ MODE: Set question type to:", problemInfo.question_type);
        }

        // Ensure we have proper MCQ structure with complete question text
        if (!problemInfo.mcq_data || !problemInfo.mcq_data.question || problemInfo.mcq_data.question.length < 50) {
          console.log("🚀 FORCE MCQ MODE: Fixing incomplete MCQ data");
          console.log("🚀 FORCE MCQ MODE: Original mcq_data:", problemInfo.mcq_data);

          // Use the full problem statement as the question for guess-output questions
          problemInfo.mcq_data = {
            question: problemInfo.problem_statement || "Code output question",
            options: problemInfo.mcq_data?.options || [] // Preserve existing options if any
          };
          console.log("🚀 FORCE MCQ MODE: Created/fixed MCQ data structure with full question text");
        }
      }

      // Store problem info in AppState
      this.deps.setProblemInfo(problemInfo);

      // Log the extracted problem info for debugging
      console.log("🔍 EXTRACTION DEBUG: Extracted problem info:", JSON.stringify(problemInfo, null, 2));

      // Send first success event
      if (mainWindow) {
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
          problemInfo
        );

        // Add a delay to allow the UI to update and show the problem statement
        // before starting to generate the solution
        console.log("Adding delay before generating solution to allow UI to update");
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Update progress status to indicate we're moving to solution generation
        mainWindow.webContents.send("processing-status", {
          message: "Problem extracted successfully. Starting solution generation...",
          progress: 50
        });

        // Generate solutions after successful extraction
        const solutionsResult = await this.generateSolutionsHelper(signal, forceMCQMode);
        if (solutionsResult.success) {
          // Clear any existing extra screenshots before transitioning to solutions view
          this.screenshotHelper.clearExtraScreenshotQueue();

          // Final progress update
          mainWindow.webContents.send("processing-status", {
            message: "Solution generated successfully",
            progress: 100
          });

          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
            solutionsResult.data
          );
          return { success: true, data: solutionsResult.data };
        } else {
          throw new Error(
            solutionsResult.error || "Failed to generate solutions"
          );
        }
      }

      return { success: false, error: "Failed to process screenshots" };
    } catch (error: any) {
      // If the request was cancelled, don't retry
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      // Handle OpenAI API errors specifically
      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      } else if (error?.response?.status === 500) {
        return {
          success: false,
          error: "OpenAI server error. Please try again later."
        };
      }

      console.error("API Error Details:", error);
      return {
        success: false,
        error: error.message || "Failed to process screenshots. Please try again."
      };
    }
  }

  private async generateSolutionsHelper(signal: AbortSignal, forceMCQMode: boolean = false) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Check if this is an MCQ, guess-output, or coding question
      const isMCQ = problemInfo.question_type === 'mcq';
      const isGuessOutput = problemInfo.question_type === 'guess-output';
      const isQuickAnswerType = isMCQ || isGuessOutput;

      // Update progress status with appropriate message
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: isQuickAnswerType
            ? (isMCQ ? "Analyzing MCQ and preparing answer..." : "Analyzing code output and preparing answer...")
            : "Creating optimal solution with detailed explanations...",
          progress: 60
        });
      }

      // If MCQ support is disabled, treat all questions as coding questions
      const mcqSupport = config.mcqSupport !== undefined ? config.mcqSupport : true;

      // Create prompt based on question type
      let promptText;

      if (isQuickAnswerType && mcqSupport) {
        console.log("🔄 QUICK ANSWER PROCESSING: Processing question:", problemInfo);
        console.log("🔄 QUICK ANSWER PROCESSING: Question type:", problemInfo.question_type);
        console.log("🔄 QUICK ANSWER PROCESSING: API Provider:", config.apiProvider);

        // Check if we have complete MCQ data (with correct_answer and explanation)
        const hasCompleteData = problemInfo.mcq_data &&
                               problemInfo.mcq_data.correct_answer &&
                               problemInfo.mcq_data.explanation;

        // For OpenRouter, always route MCQs and guess-output to QWen model for processing, even if partial data exists
        if (config.apiProvider === "openrouter") {
          console.log("🔄 QUICK ANSWER PROCESSING: OpenRouter detected - routing to QWen model");
          console.log("🔄 QUICK ANSWER PROCESSING: Data status:", hasCompleteData ? "Complete" : "Incomplete/Missing");

          // Use existing MCQ data if available, otherwise extract from problem statement
          const mcqQuestion = problemInfo.mcq_data?.question || problemInfo.problem_statement;
          const mcqOptions = problemInfo.mcq_data?.options || [];

          promptText = `
Analyze this ${isMCQ ? 'multiple-choice question' : 'code output question'} and provide the correct answer with explanation:

QUESTION:
${mcqQuestion}

${mcqOptions.length > 0 ? `OPTIONS:
${mcqOptions.map((opt: any) => `${opt.id}. ${opt.text}`).join('\n')}` : ''}

Please provide:
1. The correct answer option (A, B, C, or D)
2. A detailed explanation of why this is the correct answer
3. Brief explanations of why the other options are incorrect

Format your response as a JSON object with these fields:
{
  "question": "the full question text",
  "options": [{"id": "A", "text": "option text"}, ...],
  "correct_answer": "A",
  "explanation": "detailed explanation of the correct answer"
}

IMPORTANT: Your entire response must be valid JSON that can be parsed with JSON.parse(). Do not include any text before or after the JSON object.
`;
        } else {
          // For non-OpenRouter providers, only return directly if we have complete MCQ data
          if (hasCompleteData) {
            console.log("🔄 MCQ PROCESSING: Complete MCQ data found, returning directly:", problemInfo.mcq_data);

            // Ensure the main window is notified about the MCQ data
            if (mainWindow) {
              mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED, {
                ...problemInfo,
                question_type: 'mcq',
                mcq_data: problemInfo.mcq_data
              });
            }

            return {
              success: true,
              data: {
                solution: JSON.stringify(problemInfo.mcq_data),
                thoughts: ["This is a multiple-choice question.", "The correct answer has been identified."],
                time_complexity: "Not applicable for MCQs",
                space_complexity: "Not applicable for MCQs"
              }
            };
          } else {
            console.log("🔄 QUICK ANSWER PROCESSING: Incomplete/No data found, generating it now");
            console.log("🔄 QUICK ANSWER PROCESSING: Will route to", config.apiProvider);

            // Use existing MCQ data if available, otherwise extract from problem statement
            const mcqQuestion = problemInfo.mcq_data?.question || problemInfo.problem_statement;
            const mcqOptions = problemInfo.mcq_data?.options || [];

            promptText = `
Analyze this ${isMCQ ? 'multiple-choice question' : 'code output question'} and provide the correct answer with explanation:

QUESTION:
${mcqQuestion}

${mcqOptions.length > 0 ? `OPTIONS:
${mcqOptions.map((opt: any) => `${opt.id}. ${opt.text}`).join('\n')}` : ''}

Please provide:
1. The correct answer option (A, B, C, or D)
2. A detailed explanation of why this is the correct answer
3. Brief explanations of why the other options are incorrect

Format your response as a JSON object with these fields:
{
  "question": "the full question text",
  "options": [{"id": "A", "text": "option text"}, ...],
  "correct_answer": "A",
  "explanation": "detailed explanation of the correct answer"
}

IMPORTANT: Your entire response must be valid JSON that can be parsed with JSON.parse(). Do not include any text before or after the JSON object.
`;
          }
        }
      } else {
        // For coding problems, use the original prompt
        const defaultFunction = problemInfo.default_function_signature || "";
        const hasDefaultFunction = defaultFunction.trim().length > 0;

        promptText = `
Generate a detailed solution for the following coding problem:

PROBLEM STATEMENT:
${problemInfo.problem_statement}

CONSTRAINTS:
${problemInfo.constraints || "No specific constraints provided."}

EXAMPLE INPUT:
${problemInfo.example_input || "No example input provided."}

EXAMPLE OUTPUT:
${problemInfo.example_output || "No example output provided."}

${hasDefaultFunction ? `DEFAULT FUNCTION SIGNATURE:
${defaultFunction}

IMPORTANT: Use the exact function signature provided above. Maintain the exact function name, parameters, and return type. Do not modify the function signature in any way.` : ""}

LANGUAGE: ${language}

I need the response in the following format:
1. Code: A clean, optimized implementation in ${language}${hasDefaultFunction ? " using the provided function signature" : ""}
2. Your Thoughts: A list of key insights and reasoning behind your approach
3. Time complexity: O(X) with a detailed explanation (at least 2 sentences)
4. Space complexity: O(X) with a detailed explanation (at least 2 sentences)

${hasDefaultFunction ? `CRITICAL INSTRUCTIONS:
- You MUST use the exact function signature provided above
- Do NOT change the function name, parameter names, parameter types, or return type
- Only implement the function body
- If the problem provides a class structure, maintain it exactly as given` : `INSTRUCTIONS:
- Since no default function signature is provided, create a complete standalone solution
- Include proper main function or test cases as appropriate for ${language}`}

For complexity explanations, please be thorough. For example: "Time complexity: O(n) because we iterate through the array only once. This is optimal as we need to examine each element at least once to find the solution." or "Space complexity: O(n) because in the worst case, we store all elements in the hashmap. The additional space scales linearly with the input size."

Your solution should be efficient, well-commented, and handle edge cases.
`;
      }

      let responseContent;

      if (config.apiProvider === "openai") {
        // OpenAI processing
        if (!this.openaiClient) {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }

        // Send to OpenAI API
        const solutionResponse = await this.openaiClient.chat.completions.create({
          model: config.solutionModel || "gpt-4o",
          messages: [
            { role: "system", content: "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations." },
            { role: "user", content: promptText }
          ],
          max_tokens: 4000,
          temperature: 0.2
        });

        responseContent = solutionResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini")  {
        // Gemini processing
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try {
          // Create Gemini message structure
          const geminiMessages = [
            {
              role: "user",
              parts: [
                {
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}`
                }
              ]
            }
          ];

          // Make API request to Gemini
          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.solutionModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          responseContent = responseData.candidates[0].content.parts[0].text;
        } catch (error) {
          console.error("Error using Gemini API for solution:", error);
          return {
            success: false,
            error: "Failed to generate solution with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "openrouter") {
        // OpenRouter processing
        if (!this.openrouterApiKey) {
          return {
            success: false,
            error: "OpenRouter API key not configured. Please check your settings."
          };
        }

        // Create OpenRouter request
        // Make sure the model ID is correctly formatted for OpenRouter
        let openRouterModel = config.openrouterModel || "qwen/qwq-32b:free";

        // Special model selection for quick-answer questions when using force mode (Ctrl+Shift+S)
        // Use Mistral for both MCQ and guess-output questions triggered by Ctrl+Shift+S
        if (forceMCQMode || (isQuickAnswerType && mcqSupport)) {
          openRouterModel = "mistralai/devstral-small:free";
          console.log("🚀 QUICK PROCESSING: Using Mistral model for fast processing:", openRouterModel);
          console.log("🚀 QUICK PROCESSING: Question type:", problemInfo.question_type);
          console.log("🚀 QUICK PROCESSING: Force mode active:", forceMCQMode);
        } else {
          console.log("Using configured model with OpenRouter:", openRouterModel);
        }

        console.log("Making OpenRouter API request with model:", openRouterModel);

        // Update progress for the user
        if (mainWindow) {
          const modelName = openRouterModel.includes('mistralai') ? 'Mistral' :
                           openRouterModel.includes('qwen') ? 'Qwen' : 'OpenRouter';
          mainWindow.webContents.send("processing-status", {
            message: `Generating solution with OpenRouter (${modelName} model)...`,
            progress: 60
          });
        }

        // Adjust parameters based on model type and question complexity
        let maxTokens = 20000;
        let timeoutMs = 120000; // 2 minutes default
        let systemPrompt = "You are an expert coding interview assistant. Provide clear, optimal solutions with detailed explanations.";

        // Define model detection variables early
        const noThinkModeEnabled = config.noThinkMode !== undefined ? config.noThinkMode : true;
        const isQwenModel = openRouterModel.includes('qwen') || openRouterModel.includes('qwq');
        const isMistralModel = openRouterModel.includes('mistralai');
        // Mistral is inherently fast and doesn't need special no-think parameters
        const isFastProcessingModel = isMistralModel;

        try {

          // Special handling for QWen models and complex reasoning
          if (isQwenModel) {
            // QwQ models need more tokens and time for reasoning
            if (openRouterModel.includes('qwq')) {
              maxTokens = 25000; // Increase token limit for reasoning models
              timeoutMs = 300000; // 5 minutes for complex reasoning
            } else {
              maxTokens = 25000; // Moderate increase for other Qwen models
              timeoutMs = 300000; // 3 minutes for other Qwen models
            }

            // Enhanced system prompt for reasoning models
            systemPrompt = `You are an expert coding interview assistant with strong reasoning capabilities.
For complex problems, provide your complete thought process step by step, then give the optimal solution with detailed explanations.

CRITICAL: When a default function signature is provided in the problem, you MUST use it exactly as given. Do not modify function names, parameter names, parameter types, or return types. This is essential for platform compatibility (LeetCode, GeeksforGeeks, etc.).

Structure your response clearly with:
1. Problem Analysis
2. Approach/Algorithm
3. Step-by-step reasoning
4. Implementation (respecting any provided function signatures)
5. Complexity analysis
Be thorough but concise in your reasoning.`;
          }

          // For MCQ and guess-output questions, use different parameters
          if (isQuickAnswerType && mcqSupport) {
            console.log("🔍 QUICK ANSWER PROCESSING: Configuring OpenRouter for quick answer question");
            console.log("🔍 QUICK ANSWER PROCESSING: Question type:", problemInfo.question_type);
            console.log("🔍 QUICK ANSWER PROCESSING: Using OpenRouter model:", openRouterModel);
            maxTokens = 10000; // Quick answer questions need less tokens
            timeoutMs = 60000; // 1 minute should be enough for quick answers

            // Check if fast processing mode will be activated for this request
            // Mistral model is used for both MCQ and guess-output when in force mode or quick processing
            // QWen models only used for coding questions now
            const willActivateFastMode = isMistralModel && (forceMCQMode || isQuickAnswerType);

            if (willActivateFastMode) {
              systemPrompt = `You are an expert at analyzing ${isMCQ ? 'multiple-choice questions' : 'code output questions'}.

FAST PROCESSING MODE - DIRECT RESPONSE REQUIRED:
- Provide direct, accurate answers
- Be concise and clear
- Focus on the correct answer and explanation
- Avoid unnecessary elaboration

Respond with the requested JSON format containing the answer and explanation.`;
            } else {
              systemPrompt = `You are an expert at analyzing ${isMCQ ? 'multiple-choice questions' : 'code output questions'}. Provide clear, accurate answers with detailed explanations.`;
            }
          }

          // Log the API call details
          if (isQuickAnswerType && mcqSupport) {
            console.log("🚀 QUICK ANSWER PROCESSING: Making OpenRouter API call");
            console.log("🚀 QUICK ANSWER PROCESSING: Question type:", problemInfo.question_type);
            console.log("🚀 QUICK ANSWER PROCESSING: Model:", openRouterModel);
            console.log("🚀 QUICK ANSWER PROCESSING: Max tokens:", maxTokens);
            console.log("🚀 QUICK ANSWER PROCESSING: Timeout:", timeoutMs);
          }

          // Prepare the request body
          const requestBody: any = {
            model: openRouterModel,
            messages: [
              { role: "system", content: systemPrompt },
              { role: "user", content: promptText }
            ],
            max_tokens: maxTokens,
            temperature: 0.2,
            // Add streaming support for longer responses
            stream: false
          };

          // Apply "no think mode" for QWen models on quick answer questions OR when forced
          // (Variables already defined above)

          // Activate fast processing mode if:
          // 1. Using Mistral model for quick answers (MCQ/guess-output), OR
          // 2. Force MCQ mode is active (Ctrl+Shift+S) with Mistral model, OR
          // 3. QWen model with traditional no-think mode for coding questions
          const shouldActivateFastMode = (isMistralModel && (isQuickAnswerType || forceMCQMode)) ||
                                         (isQwenModel && isQuickAnswerType && noThinkModeEnabled);

          if (shouldActivateFastMode) {
            const modelType = isMistralModel ? 'Mistral' : isQwenModel ? 'QWen' : 'Compatible';
            console.log(`🚀 FAST MODE: Activating for ${modelType} model`);
            console.log("🚀 FAST MODE: Model:", openRouterModel);
            console.log("🚀 FAST MODE: Question type:", problemInfo.question_type);
            console.log("🚀 FAST MODE: Force MCQ mode:", forceMCQMode);
            console.log("🚀 FAST MODE: Is quick answer type:", isQuickAnswerType);

            // Modify user prompt for fast processing
            if (isMistralModel) {
              // Mistral doesn't need aggressive no-think instructions, just clear guidance
              requestBody.messages[1].content = `FAST PROCESSING REQUEST:\n\n${promptText}\n\nPlease provide a direct, accurate response in the requested JSON format.`;
            } else {
              // QWen models still need no-think instructions
              requestBody.messages[1].content = `INSTANT ANSWER REQUIRED - NO REASONING:\n\n${promptText}\n\nIMPORTANT: Respond ONLY with valid JSON. Do not include any thinking, reasoning, or explanatory text outside the JSON.`;
            }

            // Optimize for speed
            requestBody.temperature = 0.1; // Lower temperature for more direct answers
            requestBody.top_p = 0.9; // Slightly more focused sampling

            console.log("🚀 FAST MODE: Applied optimization parameters");
            console.log("🚀 FAST MODE: Final request body:", JSON.stringify(requestBody, null, 2));
          } else {
            console.log("🔄 NORMAL MODE: Fast mode not activated");
            console.log("🔄 NORMAL MODE: Conditions - isQuickAnswerType:", isQuickAnswerType, "mcqSupport:", mcqSupport, "isQwenModel:", isQwenModel, "isMistralModel:", isMistralModel, "noThinkModeEnabled:", noThinkModeEnabled, "forceMCQMode:", forceMCQMode);
          }

          console.log("📤 OPENROUTER REQUEST: Sending request with body:", JSON.stringify(requestBody, null, 2));

          const response = await axios.default.post(
            "https://openrouter.ai/api/v1/chat/completions",
            requestBody,
            {
              headers: {
                "Authorization": `Bearer ${this.openrouterApiKey}`,
                "HTTP-Referer": "https://interview-coder.com",
                "X-Title": "Interview Coder"
              },
              signal,
              timeout: timeoutMs
            }
          );

          console.log("OpenRouter API response received:", JSON.stringify(response.data, null, 2));

          if (response.data && response.data.choices && response.data.choices.length > 0) {
            responseContent = response.data.choices[0].message.content;
            console.log("OpenRouter response content extracted successfully");

            // Log quick answer processing success
            if (isQuickAnswerType && mcqSupport) {
              console.log("✅ QUICK ANSWER PROCESSING: OpenRouter API call successful");
              console.log("✅ QUICK ANSWER PROCESSING: Question type:", problemInfo.question_type);
              console.log("✅ QUICK ANSWER PROCESSING: Response length:", responseContent?.length || 0);

              if (shouldActivateFastMode) {
                const modelType = isMistralModel ? 'Mistral' : isQwenModel ? 'QWen' : 'Compatible';
                console.log(`✅ FAST MODE: Fast response achieved with ${modelType} model`);
                if (forceMCQMode) {
                  console.log("✅ FAST MODE: Activated via Ctrl+Shift+S shortcut");
                }
              }
            }

            // Check if response was truncated (common with reasoning models)
            const choice = response.data.choices[0];
            if (choice.finish_reason === 'length' && openRouterModel.includes('qwen')) {
              console.warn("Response was truncated due to length limit, but continuing with partial response");
              // For QWen models, we'll accept partial responses rather than failing
              // The response should still contain valuable reasoning even if truncated
            }

            // Update progress for the user to indicate completion
            if (mainWindow) {
              mainWindow.webContents.send("processing-status", {
                message: "Solution generated successfully",
                progress: 100
              });
            }
          } else {
            console.error("OpenRouter API response missing expected data structure:", response.data);
            throw new Error("Empty or invalid response from OpenRouter API");
          }
        } catch (error: any) {
          console.error("Error using OpenRouter API for solution:", error);

          // Check if the request was canceled by the user
          if (axios.isCancel(error) || error.code === 'ERR_CANCELED') {
            console.log("OpenRouter request was canceled by user");
            return {
              success: false,
              error: "Processing was canceled by the user."
            };
          }

          // Implement retry logic for specific error types
          if (error?.response?.status === 429 || error?.response?.status === 503 || error?.response?.status === 502) {
            console.log("Rate limit, service unavailable, or bad gateway - implementing retry logic");

            // Wait and retry once for rate limits or service issues
            await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds

            try {
              console.log("Retrying OpenRouter API request...");

              // Prepare retry request body with same parameters as original
              const retryRequestBody: any = {
                model: openRouterModel,
                messages: [
                  { role: "system", content: systemPrompt },
                  { role: "user", content: promptText }
                ],
                max_tokens: maxTokens,
                temperature: 0.2,
                stream: false
              };

              // Apply fast processing mode to retry as well if it was activated
              // Re-check the conditions for fast mode
              const retryNoThinkModeEnabled = config.noThinkMode !== undefined ? config.noThinkMode : true;
              const retryIsQwenModel = openRouterModel.includes('qwen') || openRouterModel.includes('qwq');
              const retryIsMistralModel = openRouterModel.includes('mistralai');
              const retryFastMode = (retryIsMistralModel && (isQuickAnswerType || forceMCQMode)) ||
                                   (retryIsQwenModel && isQuickAnswerType && retryNoThinkModeEnabled);

              if (retryFastMode) {
                console.log("🚀 FAST MODE: Applying to retry request");
                // Modify user prompt for fast processing in retry
                if (retryIsMistralModel) {
                  retryRequestBody.messages[1].content = `FAST PROCESSING REQUEST:\n\n${promptText}\n\nPlease provide a direct, accurate response in the requested JSON format.`;
                } else {
                  retryRequestBody.messages[1].content = `INSTANT ANSWER REQUIRED - NO REASONING:\n\n${promptText}\n\nIMPORTANT: Respond ONLY with valid JSON. Do not include any thinking, reasoning, or explanatory text outside the JSON.`;
                }
                retryRequestBody.temperature = 0.1;
                retryRequestBody.top_p = 0.9;
              }

              const retryResponse = await axios.default.post(
                "https://openrouter.ai/api/v1/chat/completions",
                retryRequestBody,
                {
                  headers: {
                    "Authorization": `Bearer ${this.openrouterApiKey}`,
                    "HTTP-Referer": "https://interview-coder.com",
                    "X-Title": "Interview Coder"
                  },
                  signal,
                  timeout: timeoutMs
                }
              );

              if (retryResponse.data && retryResponse.data.choices && retryResponse.data.choices.length > 0) {
                responseContent = retryResponse.data.choices[0].message.content;
                console.log("Retry successful");

                // Check for truncation on retry as well
                const retryChoice = retryResponse.data.choices[0];
                if (retryChoice.finish_reason === 'length' && openRouterModel.includes('qwen')) {
                  console.warn("Retry response was also truncated, but continuing with partial response");
                }
              } else {
                throw new Error("Empty response from OpenRouter API on retry");
              }
            } catch (retryError: any) {
              console.error("Retry also failed:", retryError);

              // Check if retry was also canceled
              if (axios.isCancel(retryError) || retryError.code === 'ERR_CANCELED') {
                return {
                  success: false,
                  error: "Processing was canceled by the user."
                };
              }

              return {
                success: false,
                error: "Failed to generate solution with OpenRouter API after retry. The service may be temporarily unavailable. Please try again later."
              };
            }
          } else {
            // More detailed error logging for non-retryable errors
            if (error.response) {
              // The request was made and the server responded with a status code
              // that falls out of the range of 2xx
              console.error("OpenRouter API error response data:", error.response.data);
              console.error("OpenRouter API error response status:", error.response.status);
              console.error("OpenRouter API error response headers:", error.response.headers);

              return {
                success: false,
                error: `OpenRouter API error (${error.response.status}): ${JSON.stringify(error.response.data)}`
              };
            } else if (error.request) {
              // The request was made but no response was received
              console.error("OpenRouter API no response received:", error.request);

              // Check if this is a cancellation error in the request object
              if (axios.isCancel(error) || error.code === 'ERR_CANCELED') {
                return {
                  success: false,
                  error: "Processing was canceled by the user."
                };
              }

              return {
                success: false,
                error: "No response received from OpenRouter API. Please check your internet connection."
              };
            } else {
              // Something happened in setting up the request that triggered an Error
              console.error("OpenRouter API request setup error:", error.message);

              // Check if this is a cancellation error
              if (axios.isCancel(error) || error.code === 'ERR_CANCELED') {
                return {
                  success: false,
                  error: "Processing was canceled by the user."
                };
              }

              return {
                success: false,
                error: `Failed to generate solution with OpenRouter API: ${error.message}`
              };
            }
          }
        }
      } else if (config.apiProvider === "anthropic") {
        // Anthropic processing
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try {
          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: `You are an expert coding interview assistant. Provide a clear, optimal solution with detailed explanations for this problem:\n\n${promptText}`
                }
              ]
            }
          ];

          // Send to Anthropic API
          const response = await this.anthropicClient.messages.create({
            model: config.solutionModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          responseContent = (response.content[0] as { type: 'text', text: string }).text;
        } catch (error: any) {
          console.error("Error using Anthropic API for solution:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to generate solution with Anthropic API. Please check your API key or try again later."
          };
        }
      }

      // Check if this is a quick answer response (MCQ or guess-output) - reuse the problemInfo from above

      if (isQuickAnswerType) {
        console.log("Processing quick answer response:", responseContent);
        console.log("Question type:", problemInfo.question_type);

        // Try to parse the response as JSON with improved error handling
        try {
          // First, try to extract JSON if it's wrapped in text
          let jsonContent = responseContent;

          // Look for JSON object pattern - more robust extraction
          const jsonMatch = responseContent.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            jsonContent = jsonMatch[0];
          }

          // Clean up common JSON formatting issues before parsing
          jsonContent = this.cleanJsonForParsing(jsonContent);

          // Parse the JSON
          const answerData = JSON.parse(jsonContent);
          console.log("Successfully parsed answer data:", answerData);

          // Validate the answer data
          if (answerData.question && answerData.options && answerData.correct_answer && answerData.explanation) {
            // Update the problem info with the answer data
            if (this.deps.getMainWindow()) {
              this.deps.getMainWindow().webContents.send(this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED, {
                ...problemInfo,
                question_type: problemInfo.question_type, // Keep original type (mcq or guess-output)
                mcq_data: answerData
              });
            }

            // Return the answer data
            return {
              success: true,
              data: {
                solution: JSON.stringify(answerData),
                thoughts: [
                  `This is a ${isMCQ ? 'multiple-choice question' : 'code output question'}.`,
                  "The correct answer has been identified."
                ],
                time_complexity: `Not applicable for ${isMCQ ? 'MCQs' : 'code output questions'}`,
                space_complexity: `Not applicable for ${isMCQ ? 'MCQs' : 'code output questions'}`
              }
            };
          } else {
            console.error("Invalid answer data format:", answerData);
          }
        } catch (error) {
          console.error("Error parsing quick answer response:", error);

          // Try alternative parsing methods for malformed JSON
          try {
            const fallbackMcqData = this.extractMcqDataFromText(responseContent);
            if (fallbackMcqData) {
              console.log("Successfully extracted MCQ data using fallback method:", fallbackMcqData);

              // Update the problem info with the MCQ data
              if (this.deps.getMainWindow()) {
                this.deps.getMainWindow().webContents.send(this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED, {
                  ...problemInfo,
                  question_type: 'mcq',
                  mcq_data: fallbackMcqData
                });
              }

              // Return the answer data
              return {
                success: true,
                data: {
                  solution: JSON.stringify(fallbackMcqData),
                  thoughts: [
                    `This is a ${isMCQ ? 'multiple-choice question' : 'code output question'}.`,
                    "The correct answer has been identified."
                  ],
                  time_complexity: `Not applicable for ${isMCQ ? 'MCQs' : 'code output questions'}`,
                  space_complexity: `Not applicable for ${isMCQ ? 'MCQs' : 'code output questions'}`
                }
              };
            }
          } catch (fallbackError) {
            console.error("Fallback quick answer parsing also failed:", fallbackError);
          }

          // Continue with normal processing as fallback
        }
      }

      // Normal code extraction for coding problems
      const codeMatch = responseContent.match(/```(?:\w+)?\s*([\s\S]*?)```/);
      const code = codeMatch ? codeMatch[1].trim() : responseContent;

      // Extract thoughts, looking for bullet points or numbered lists
      const thoughtsRegex = /(?:Thoughts:|Key Insights:|Reasoning:|Approach:)([\s\S]*?)(?:Time complexity:|$)/i;
      const thoughtsMatch = responseContent.match(thoughtsRegex);
      let thoughts: string[] = [];

      if (thoughtsMatch && thoughtsMatch[1]) {
        // Extract bullet points or numbered items
        const bulletPoints = thoughtsMatch[1].match(/(?:^|\n)\s*(?:[-*•]|\d+\.)\s*(.*)/g);
        if (bulletPoints) {
          thoughts = bulletPoints.map((point: string) =>
            point.replace(/^\s*(?:[-*•]|\d+\.)\s*/, '').trim()
          ).filter(Boolean);
        } else {
          // If no bullet points found, split by newlines and filter empty lines
          thoughts = thoughtsMatch[1].split('\n')
            .map((line: string) => line.trim())
            .filter(Boolean);
        }
      }

      // Extract complexity information
      const timeComplexityPattern = /Time complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:Space complexity|$))/i;
      const spaceComplexityPattern = /Space complexity:?\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\s*(?:[A-Z]|$))/i;

      let timeComplexity = "O(n) - Linear time complexity because we only iterate through the array once. Each element is processed exactly one time, and the hashmap lookups are O(1) operations.";
      let spaceComplexity = "O(n) - Linear space complexity because we store elements in the hashmap. In the worst case, we might need to store all elements before finding the solution pair.";

      const timeMatch = responseContent.match(timeComplexityPattern);
      if (timeMatch && timeMatch[1]) {
        timeComplexity = timeMatch[1].trim();
        if (!timeComplexity.match(/O\([^)]+\)/i)) {
          timeComplexity = `O(n) - ${timeComplexity}`;
        } else if (!timeComplexity.includes('-') && !timeComplexity.includes('because')) {
          const notationMatch = timeComplexity.match(/O\([^)]+\)/i);
          if (notationMatch) {
            const notation = notationMatch[0];
            const rest = timeComplexity.replace(notation, '').trim();
            timeComplexity = `${notation} - ${rest}`;
          }
        }
      }

      const spaceMatch = responseContent.match(spaceComplexityPattern);
      if (spaceMatch && spaceMatch[1]) {
        spaceComplexity = spaceMatch[1].trim();
        if (!spaceComplexity.match(/O\([^)]+\)/i)) {
          spaceComplexity = `O(n) - ${spaceComplexity}`;
        } else if (!spaceComplexity.includes('-') && !spaceComplexity.includes('because')) {
          const notationMatch = spaceComplexity.match(/O\([^)]+\)/i);
          if (notationMatch) {
            const notation = notationMatch[0];
            const rest = spaceComplexity.replace(notation, '').trim();
            spaceComplexity = `${notation} - ${rest}`;
          }
        }
      }

      const formattedResponse = {
        code: code,
        thoughts: thoughts.length > 0 ? thoughts : ["Solution approach based on efficiency and readability"],
        time_complexity: timeComplexity,
        space_complexity: spaceComplexity
      };

      return { success: true, data: formattedResponse };
    } catch (error: any) {
      if (axios.isCancel(error)) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        };
      }

      if (error?.response?.status === 401) {
        return {
          success: false,
          error: "Invalid OpenAI API key. Please check your settings."
        };
      } else if (error?.response?.status === 429) {
        return {
          success: false,
          error: "OpenAI API rate limit exceeded or insufficient credits. Please try again later."
        };
      }

      console.error("Solution generation error:", error);
      return { success: false, error: error.message || "Failed to generate solution" };
    }
  }

  private async processExtraScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const problemInfo = this.deps.getProblemInfo();
      const language = await this.getLanguage();
      const config = configHelper.loadConfig();
      const mainWindow = this.deps.getMainWindow();

      if (!problemInfo) {
        throw new Error("No problem info available");
      }

      // Update progress status
      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Processing debug screenshots...",
          progress: 30
        });
      }

      // Prepare the images for the API call
      const imageDataList = screenshots.map(screenshot => screenshot.data);

      let debugContent: string = "";

      if (config.apiProvider === "openai") {
        if (!this.openaiClient) {
          return {
            success: false,
            error: "OpenAI API key not configured. Please check your settings."
          };
        }

        const messages = [
          {
            role: "system" as const,
            content: `You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

Your response MUST follow this exact structure with these section headers (use ### for headers):
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).`
          },
          {
            role: "user" as const,
            content: [
              {
                type: "text" as const,
                text: `I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution. Here are screenshots of my code, the errors or test cases. Please provide a detailed analysis with:
1. What issues you found in my code
2. Specific improvements and corrections
3. Any optimizations that would make the solution better
4. A clear explanation of the changes needed`
              },
              ...imageDataList.map(data => ({
                type: "image_url" as const,
                image_url: { url: `data:image/png;base64,${data}` }
              }))
            ]
          }
        ];

        if (mainWindow) {
          mainWindow.webContents.send("processing-status", {
            message: "Analyzing code and generating debug feedback...",
            progress: 60
          });
        }

        const debugResponse = await this.openaiClient.chat.completions.create({
          model: config.debuggingModel || "gpt-4o",
          messages: messages,
          max_tokens: 4000,
          temperature: 0.2
        });

        if (!debugResponse.choices || debugResponse.choices.length === 0 || !debugResponse.choices[0].message.content) {
          throw new Error("Empty response from OpenAI API");
        }

        debugContent = debugResponse.choices[0].message.content;
      } else if (config.apiProvider === "gemini")  {
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured. Please check your settings."
          };
        }

        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).
`;

          const geminiMessages = [
            {
              role: "user",
              parts: [
                { text: debugPrompt },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Gemini...",
              progress: 60
            });
          }

          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.debuggingModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          if (!responseData.candidates[0].content || !responseData.candidates[0].content.parts ||
              responseData.candidates[0].content.parts.length === 0 ||
              !responseData.candidates[0].content.parts[0].text) {
            throw new Error("Invalid response structure from Gemini API");
          }

          debugContent = responseData.candidates[0].content.parts[0].text;
        } catch (error) {
          console.error("Error using Gemini API for debugging:", error);
          return {
            success: false,
            error: "Failed to process debug request with Gemini API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "anthropic") {
        if (!this.anthropicClient) {
          return {
            success: false,
            error: "Anthropic API key not configured. Please check your settings."
          };
        }

        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification.
`;

          const messages = [
            {
              role: "user" as const,
              content: [
                {
                  type: "text" as const,
                  text: debugPrompt
                },
                ...imageDataList.map(data => ({
                  type: "image" as const,
                  source: {
                    type: "base64" as const,
                    media_type: "image/png" as const,
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Claude...",
              progress: 60
            });
          }

          const response = await this.anthropicClient.messages.create({
            model: config.debuggingModel || "claude-3-7-sonnet-20250219",
            max_tokens: 4000,
            messages: messages,
            temperature: 0.2
          });

          if (!response.content || response.content.length === 0 ||
              !response.content[0] || response.content[0].type !== 'text' ||
              !(response.content[0] as { type: 'text', text: string }).text) {
            throw new Error("Invalid response structure from Anthropic API");
          }

          debugContent = (response.content[0] as { type: 'text', text: string }).text;
        } catch (error: any) {
          console.error("Error using Anthropic API for debugging:", error);

          // Add specific handling for Claude's limitations
          if (error.status === 429) {
            return {
              success: false,
              error: "Claude API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error.status === 413 || (error.message && error.message.includes("token"))) {
            return {
              success: false,
              error: "Your screenshots contain too much information for Claude to process. Switch to OpenAI or Gemini in settings which can handle larger inputs."
            };
          }

          return {
            success: false,
            error: "Failed to process debug request with Anthropic API. Please check your API key or try again later."
          };
        }
      } else if (config.apiProvider === "openrouter") {
        // OpenRouter debugging support - use Gemini for image analysis
        if (!this.geminiApiKey) {
          return {
            success: false,
            error: "Gemini API key not configured for image processing. Please add a Gemini API key in settings for OpenRouter debugging."
          };
        }

        try {
          const debugPrompt = `
You are a coding interview assistant helping debug and improve solutions. Analyze these screenshots which include either error messages, incorrect outputs, or test cases, and provide detailed debugging help.

I'm solving this coding problem: "${problemInfo.problem_statement}" in ${language}. I need help with debugging or improving my solution.

YOUR RESPONSE MUST FOLLOW THIS EXACT STRUCTURE WITH THESE SECTION HEADERS:
### Issues Identified
- List each issue as a bullet point with clear explanation

### Specific Improvements and Corrections
- List specific code changes needed as bullet points

### Optimizations
- List any performance optimizations if applicable

### Explanation of Changes Needed
Here provide a clear explanation of why the changes are needed

### Key Points
- Summary bullet points of the most important takeaways

If you include code examples, use proper markdown code blocks with language specification (e.g. \`\`\`java).
`;

          const geminiMessages = [
            {
              role: "user",
              parts: [
                { text: debugPrompt },
                ...imageDataList.map(data => ({
                  inlineData: {
                    mimeType: "image/png",
                    data: data
                  }
                }))
              ]
            }
          ];

          if (mainWindow) {
            mainWindow.webContents.send("processing-status", {
              message: "Analyzing code and generating debug feedback with Gemini (for OpenRouter)...",
              progress: 60
            });
          }

          const response = await axios.default.post(
            `https://generativelanguage.googleapis.com/v1beta/models/${config.debuggingModel || "gemini-2.0-flash"}:generateContent?key=${this.geminiApiKey}`,
            {
              contents: geminiMessages,
              generationConfig: {
                temperature: 0.2,
                maxOutputTokens: 4000
              }
            },
            { signal }
          );

          const responseData = response.data as GeminiResponse;

          if (!responseData.candidates || responseData.candidates.length === 0) {
            throw new Error("Empty response from Gemini API");
          }

          if (!responseData.candidates[0].content || !responseData.candidates[0].content.parts ||
              responseData.candidates[0].content.parts.length === 0 ||
              !responseData.candidates[0].content.parts[0].text) {
            throw new Error("Invalid response structure from Gemini API");
          }

          debugContent = responseData.candidates[0].content.parts[0].text;
        } catch (error: any) {
          console.error("Error using Gemini API for OpenRouter debugging:", error);

          if (error?.response?.status === 429) {
            return {
              success: false,
              error: "Gemini API rate limit exceeded. Please wait a few minutes before trying again."
            };
          } else if (error?.response?.status === 402 || error?.response?.status === 403) {
            return {
              success: false,
              error: "Gemini API quota exceeded or access denied. Please check your Gemini API key and quota."
            };
          }

          return {
            success: false,
            error: "Failed to process debug request with Gemini API (for OpenRouter). Please check your Gemini API key or try again later."
          };
        }
      } else {
        // Fallback for unsupported API provider or missing configuration
        return {
          success: false,
          error: `Unsupported API provider: ${config.apiProvider}. Please configure a valid API provider (openai, gemini, anthropic, or openrouter) in settings.`
        };
      }

      // Ensure debugContent is not null or undefined
      if (!debugContent) {
        return {
          success: false,
          error: "No debug content received from API. Please try again or check your API configuration."
        };
      }

      if (mainWindow) {
        mainWindow.webContents.send("processing-status", {
          message: "Debug analysis complete",
          progress: 100
        });
      }

      let extractedCode = "// Debug mode - see analysis below";
      if (debugContent && typeof debugContent === 'string') {
        const codeMatch = debugContent.match(/```(?:[a-zA-Z]+)?([\s\S]*?)```/);
        if (codeMatch && codeMatch[1]) {
          extractedCode = codeMatch[1].trim();
        }
      }

      let formattedDebugContent = debugContent || "Debug analysis not available";

      if (debugContent && typeof debugContent === 'string') {
        if (!debugContent.includes('# ') && !debugContent.includes('## ')) {
          formattedDebugContent = debugContent
            .replace(/issues identified|problems found|bugs found/i, '## Issues Identified')
            .replace(/code improvements|improvements|suggested changes/i, '## Code Improvements')
            .replace(/optimizations|performance improvements/i, '## Optimizations')
            .replace(/explanation|detailed analysis/i, '## Explanation');
        }
      }

      const bulletPoints = formattedDebugContent && typeof formattedDebugContent === 'string'
        ? formattedDebugContent.match(/(?:^|\n)[ ]*(?:[-*•]|\d+\.)[ ]+([^\n]+)/g)
        : null;
      const thoughts = bulletPoints
        ? bulletPoints.map(point => point.replace(/^[ ]*(?:[-*•]|\d+\.)[ ]+/, '').trim()).slice(0, 5)
        : ["Debug analysis based on your screenshots"];

      const response = {
        code: extractedCode,
        debug_analysis: formattedDebugContent,
        thoughts: thoughts,
        time_complexity: "N/A - Debug mode",
        space_complexity: "N/A - Debug mode"
      };

      return { success: true, data: response };
    } catch (error: any) {
      console.error("Debug processing error:", error);
      return { success: false, error: error.message || "Failed to process debug request" };
    }
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    this.deps.setHasDebugged(false)

    this.deps.setProblemInfo(null)

    const mainWindow = this.deps.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
    }
  }

  /**
   * Clean JSON content to fix common formatting issues that cause parsing errors
   */
  private cleanJsonForParsing(jsonContent: string): string {
    try {
      // Remove any markdown code block markers
      jsonContent = jsonContent.replace(/```json|```/g, '').trim();

      // Remove any leading/trailing whitespace
      jsonContent = jsonContent.trim();

      // More robust approach: Parse field by field and reconstruct
      // This handles complex cases like multiline explanations with quotes

      // Try to extract each field individually and reconstruct the JSON
      const questionMatch = jsonContent.match(/"question"\s*:\s*"([^"]+)"/);
      const correctAnswerMatch = jsonContent.match(/"correct_answer"\s*:\s*"([^"]+)"/);

      // Extract options array
      const optionsMatch = jsonContent.match(/"options"\s*:\s*\[([\s\S]*?)\]/);
      let optionsArray = [];
      if (optionsMatch) {
        try {
          // Try to parse the options array
          const optionsStr = '[' + optionsMatch[1] + ']';
          optionsArray = JSON.parse(optionsStr);
        } catch {
          // Fallback: extract options manually
          const optionMatches = jsonContent.matchAll(/\{\s*"id"\s*:\s*"([A-D])"\s*,\s*"text"\s*:\s*"([^"]+)"\s*\}/g);
          optionsArray = Array.from(optionMatches).map(match => ({
            id: match[1],
            text: match[2]
          }));
        }
      }

      // Extract explanation - this is the most problematic field
      let explanation = "Explanation not available";
      const explanationMatch = jsonContent.match(/"explanation"\s*:\s*"([\s\S]*?)"\s*\}/);
      if (explanationMatch) {
        explanation = explanationMatch[1];
        // Clean up the explanation
        explanation = explanation
          .replace(/\\n/g, '\n')  // Convert escaped newlines back
          .replace(/\\"/g, '"')   // Convert escaped quotes back
          .replace(/\n/g, ' ')    // Convert actual newlines to spaces
          .replace(/"/g, '\\"')   // Escape quotes for JSON
          .trim();
      }

      // Reconstruct the JSON object
      const cleanedData = {
        question: questionMatch ? questionMatch[1] : "",
        options: optionsArray,
        correct_answer: correctAnswerMatch ? correctAnswerMatch[1] : "",
        explanation: explanation
      };

      // Return the cleaned JSON string
      return JSON.stringify(cleanedData);

    } catch (error) {
      console.error("Error cleaning JSON:", error);

      // Fallback: Basic cleaning approach
      try {
        // Remove any markdown code block markers
        let fallbackContent = jsonContent.replace(/```json|```/g, '').trim();

        // Fix tab characters
        fallbackContent = fallbackContent.replace(/\t/g, '\\t');

        // Try to fix unescaped quotes in a simple way
        fallbackContent = fallbackContent.replace(/([^\\])"/g, '$1\\"');
        if (fallbackContent.startsWith('"')) {
          fallbackContent = '\\"' + fallbackContent.substring(1);
        }

        return fallbackContent;
      } catch {
        return jsonContent; // Return original if all cleaning fails
      }
    }
  }

  /**
   * Extract MCQ data from text when JSON parsing fails
   * This is a fallback method that uses regex patterns to extract the data
   */
  private extractMcqDataFromText(responseContent: string): any | null {
    try {
      console.log("Attempting fallback MCQ extraction from text");

      // Try to extract the question
      const questionMatch = responseContent.match(/(?:"question":\s*"([^"]+)"|Question:\s*([^\n]+))/i);
      const question = questionMatch ? (questionMatch[1] || questionMatch[2]) : null;

      if (!question) {
        console.log("Could not extract question from response");
        return null;
      }

      // Try to extract options
      const options: Array<{id: string, text: string}> = [];

      // Look for options in various formats
      const optionPatterns = [
        /(?:"id":\s*"([A-D])",\s*"text":\s*"([^"]+)")/g,
        /([A-D])\.?\s*([^\n]+)/g,
        /Option\s+([A-D]):\s*([^\n]+)/gi
      ];

      for (const pattern of optionPatterns) {
        let match;
        while ((match = pattern.exec(responseContent)) !== null) {
          const id = match[1].toUpperCase();
          const text = match[2].trim();

          // Avoid duplicates
          if (!options.find(opt => opt.id === id) && text.length > 0) {
            options.push({ id, text });
          }
        }

        if (options.length >= 2) break; // If we found options, stop looking
      }

      if (options.length === 0) {
        console.log("Could not extract options from response");
        return null;
      }

      // Try to extract correct answer
      const answerMatch = responseContent.match(/(?:"correct_answer":\s*"([A-D])"|correct answer[:\s]+([A-D])|answer[:\s]+([A-D]))/i);
      const correctAnswer = answerMatch ? (answerMatch[1] || answerMatch[2] || answerMatch[3]) : null;

      if (!correctAnswer) {
        console.log("Could not extract correct answer from response");
        return null;
      }

      // Try to extract explanation
      const explanationPatterns = [
        /"explanation":\s*"([^"]+)"/,
        /explanation[:\s]+([^}]+?)(?:\n|$)/i,
        /because\s+([^.]+\.)/i
      ];

      let explanation = "Explanation not available";
      for (const pattern of explanationPatterns) {
        const explanationMatch = responseContent.match(pattern);
        if (explanationMatch && explanationMatch[1]) {
          explanation = explanationMatch[1].trim();
          break;
        }
      }

      const extractedData = {
        question: question.trim(),
        options: options,
        correct_answer: correctAnswer.toUpperCase(),
        explanation: explanation
      };

      console.log("Successfully extracted MCQ data:", extractedData);
      return extractedData;

    } catch (error) {
      console.error("Error in fallback MCQ extraction:", error);
      return null;
    }
  }
}
